'use client'

import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { RequireAuthWrapper } from '@/components/auth/ProtectedRoute'

export default function DashboardPage() {
  const { user, logout } = useAuth()
  const router = useRouter()

  const handleLogout = () => {
    logout()
    router.push('/')
  }

  return (
    <RequireAuthWrapper>
      <div className="min-h-screen bg-background p-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
              <p className="text-muted mt-1">Welcome back, {user?.name || user?.email || 'User'}!</p>
            </div>
            <Button onClick={handleLogout} variant="outline">
              Sign Out
            </Button>
          </div>

          {/* User Info Card */}
          <Card className="mb-6">
            <CardHeader>
              <h2 className="text-xl font-semibold text-foreground">User Information</h2>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <span className="font-medium text-foreground">Email:</span>
                  <span className="ml-2 text-muted">{user?.email || 'N/A'}</span>
                </div>
                {user?.name && (
                  <div>
                    <span className="font-medium text-foreground">Name:</span>
                    <span className="ml-2 text-muted">{user.name}</span>
                  </div>
                )}
                <div>
                  <span className="font-medium text-foreground">User ID:</span>
                  <span className="ml-2 text-muted font-mono text-sm">{user?.id || 'N/A'}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Welcome Message */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold text-foreground">Welcome to MRH</h2>
            </CardHeader>
            <CardContent>
              <p className="text-muted mb-4">
                You have successfully authenticated! This is a protected dashboard page that requires authentication to
                access.
              </p>
              <p className="text-muted">The authentication system includes:</p>
              <ul className="list-disc list-inside text-muted mt-2 space-y-1">
                <li>Secure JWT-based authentication</li>
                <li>Password hashing with bcrypt</li>
                <li>Form validation and error handling</li>
                <li>Responsive design with lime green theme</li>
                <li>Protected routes and authentication context</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </RequireAuthWrapper>
  )
}
